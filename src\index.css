@tailwind base;
@tailwind components;
@tailwind utilities;

/* NEO-BRUTALIST STORY ANALYZER - Design System
Bold, high-contrast design with geometric shapes and stark typography.
*/

@layer base {
  :root {
    /* Neo-Brutalist Base - High Contrast */
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;

    /* Cards & Panels - Strategic Greys for Contrast */
    --card: 0 0% 92%;
    --card-foreground: 0 0% 0%;

    --popover: 0 0% 95%;
    --popover-foreground: 0 0% 0%;

    /* Primary - Electric Yellow */
    --primary: 60 100% 50%;
    --primary-foreground: 0 0% 0%;

    /* Secondary - Deep Black */
    --secondary: 0 0% 0%;
    --secondary-foreground: 0 0% 100%;

    /* Muted - Medium Gray for Sections */
    --muted: 0 0% 88%;
    --muted-foreground: 0 0% 30%;

    /* Accent - Hot Pink */
    --accent: 320 100% 50%;
    --accent-foreground: 0 0% 100%;

    /* Destructive - Bright Red */
    --destructive: 0 100% 50%;
    --destructive-foreground: 0 0% 100%;

    /* Borders & Inputs - Strong Black */
    --border: 0 0% 0%;
    --input: 0 0% 100%;
    --ring: 60 100% 50%;

    /* Neo-Brutalist Specific Colors */
    --electric-yellow: 60 100% 50%;
    --hot-pink: 320 100% 50%;
    --bright-red: 0 100% 50%;
    --electric-blue: 200 100% 50%;
    --lime-green: 90 100% 50%;
    --deep-black: 0 0% 0%;

    /* Gradients - Bold & Geometric */
    --gradient-brutalist: linear-gradient(90deg, hsl(var(--electric-yellow)) 0%, hsl(var(--hot-pink)) 100%);
    --gradient-stark: linear-gradient(180deg, hsl(var(--background)) 0%, hsl(var(--muted)) 100%);
    --gradient-bold: linear-gradient(45deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 50%, hsl(var(--bright-red)) 100%);

    /* Shadows - Bold & Harsh */
    --shadow-brutal: 8px 8px 0px hsl(var(--deep-black));
    --shadow-primary: 4px 4px 0px hsl(var(--primary));
    --shadow-accent: 4px 4px 0px hsl(var(--accent));
    --shadow-inset: inset 4px 4px 0px hsl(var(--deep-black));

    /* Animations */
    --transition-brutal: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    --radius: 0rem;

    --sidebar-background: 0 0% 90%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 60 100% 50%;
    --sidebar-primary-foreground: 0 0% 0%;
    --sidebar-accent: 0 0% 85%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 0%;
    --sidebar-ring: 60 100% 50%;
  }

  .dark {
    /* Dark mode - Inverted but still brutal */
    --background: 0 0% 0%;
    --foreground: 0 0% 100%;
    --card: 0 0% 8%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 5%;
    --popover-foreground: 0 0% 100%;
    --muted: 0 0% 15%;
    --muted-foreground: 0 0% 70%;
    --input: 0 0% 5%;
    --border: 0 0% 100%;
    --shadow-brutal: 8px 8px 0px hsl(var(--foreground));
    --shadow-primary: 4px 4px 0px hsl(var(--primary));
    --shadow-accent: 4px 4px 0px hsl(var(--accent));
  }
}

@layer components {
  /* Neo-Brutalist UI Components */
  .brutal-panel {
    @apply bg-card border-4 border-border;
    box-shadow: var(--shadow-brutal);
  }

  .brutal-button {
    @apply relative overflow-hidden transition-all duration-200 border-4 border-border;
    box-shadow: var(--shadow-primary);
  }

  .brutal-button:hover {
    transform: translate(2px, 2px);
    box-shadow: 2px 2px 0px hsl(var(--border));
  }

  .brutal-button:active {
    transform: translate(4px, 4px);
    box-shadow: none;
  }

  .brutal-text {
    @apply font-space-grotesk font-black uppercase tracking-wide;
    color: hsl(var(--foreground));
  }

  .brutal-heading {
    @apply font-space-grotesk font-black text-4xl uppercase tracking-wider;
    color: hsl(var(--foreground));
    text-shadow: 4px 4px 0px hsl(var(--primary));
  }

  .brutal-card {
    @apply bg-card border-4 border-border p-6;
    box-shadow: var(--shadow-brutal);
  }

  .brutal-input {
    @apply bg-input border-4 border-border text-foreground;
    box-shadow: var(--shadow-inset);
  }

  .brutal-input:focus {
    @apply ring-0 border-primary;
    box-shadow: var(--shadow-primary);
  }

  .geometric-bg {
    background: repeating-linear-gradient(
      45deg,
      hsl(var(--muted)),
      hsl(var(--muted)) 10px,
      transparent 10px,
      transparent 20px
    );
  }
}

@layer utilities {
  .shadow-brutal {
    box-shadow: var(--shadow-brutal);
  }

  .shadow-primary {
    box-shadow: var(--shadow-primary);
  }

  .shadow-accent {
    box-shadow: var(--shadow-accent);
  }

  .transition-brutal {
    transition: var(--transition-brutal);
  }

  .gradient-brutalist {
    background: var(--gradient-brutalist);
  }

  .gradient-bold {
    background: var(--gradient-bold);
  }
}

/* Neo-Brutalist Animations */
@keyframes brutal-shake {
  0%, 100% { transform: translate(0); }
  25% { transform: translate(-2px, 2px); }
  50% { transform: translate(2px, -2px); }
  75% { transform: translate(-2px, -2px); }
}

@keyframes brutal-pulse {
  0%, 100% { 
    transform: scale(1);
    box-shadow: var(--shadow-brutal);
  }
  50% { 
    transform: scale(1.05);
    box-shadow: var(--shadow-primary);
  }
}

@keyframes slide-in-brutal {
  0% { 
    transform: translateX(-100%);
    box-shadow: none;
  }
  100% { 
    transform: translateX(0);
    box-shadow: var(--shadow-brutal);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}