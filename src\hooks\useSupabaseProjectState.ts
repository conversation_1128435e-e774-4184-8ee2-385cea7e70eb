import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { validateInput, sanitizeInput } from '@/utils/validation';
import type { Database } from '@/integrations/supabase/types';

type Project = Database['public']['Tables']['projects']['Row'];
type AnalysisResult = Database['public']['Tables']['analysis_results']['Row'];
type ChatMessage = Database['public']['Tables']['chat_messages']['Row'];

interface ProjectWithData extends Project {
  analysis_results?: AnalysisResult[];
  chat_messages?: ChatMessage[];
}

export const useSupabaseProjectState = () => {
  const [projects, setProjects] = useState<ProjectWithData[]>([]);
  const [currentProject, setCurrentProject] = useState<ProjectWithData | null>(null);
  const [loading, setLoading] = useState(true);
  const { toast } = useToast();

  // Fetch user's projects
  const fetchProjects = async () => {
    try {
      const { data, error } = await supabase
        .from('projects')
        .select(`
          *,
          analysis_results(*),
          chat_messages(*)
        `)
        .order('created_at', { ascending: false });

      if (error) throw error;
      setProjects(data || []);
    } catch (error: any) {
      toast({
        title: "Error loading projects",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Create new project
  const createProject = async (title: string, description?: string, content?: string) => {
    try {
      // Validate inputs
      const titleValidation = validateInput.projectName(title);
      if (!titleValidation.isValid) {
        throw new Error(titleValidation.error);
      }

      if (description) {
        const descValidation = validateInput.description(description);
        if (!descValidation.isValid) {
          throw new Error(descValidation.error);
        }
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      // Sanitize inputs
      const sanitizedTitle = sanitizeInput.text(title);
      const sanitizedDescription = description ? sanitizeInput.text(description) : undefined;

      const { data, error } = await supabase
        .from('projects')
        .insert({
          user_id: user.id,
          title: sanitizedTitle,
          description: sanitizedDescription,
          content,
          status: 'draft'
        })
        .select()
        .single();

      if (error) throw error;

      await fetchProjects();
      return data.id;
    } catch (error: any) {
      toast({
        title: "Error creating project",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };

  // Delete project
  const deleteProject = async (projectId: string) => {
    try {
      const { error } = await supabase
        .from('projects')
        .delete()
        .eq('id', projectId);

      if (error) throw error;

      await fetchProjects();
      if (currentProject?.id === projectId) {
        setCurrentProject(null);
      }
    } catch (error: any) {
      toast({
        title: "Error deleting project",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Set current project
  const selectProject = async (projectId: string) => {
    const project = projects.find(p => p.id === projectId);
    if (project) {
      setCurrentProject(project);
    }
  };

  // Update project content
  const updateProject = async (projectId: string, updates: Partial<Pick<Project, 'title' | 'description' | 'content' | 'status' | 'file_name' | 'file_size'>>) => {
    try {
      const { error } = await supabase
        .from('projects')
        .update(updates)
        .eq('id', projectId);

      if (error) throw error;

      await fetchProjects();
    } catch (error: any) {
      toast({
        title: "Error updating project",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Add analysis result
  const addAnalysisResult = async (projectId: string, analysisType: string, resultData: any, confidenceScore?: number) => {
    try {
      const { error } = await supabase
        .from('analysis_results')
        .insert({
          project_id: projectId,
          analysis_type: analysisType,
          result_data: resultData,
          confidence_score: confidenceScore
        });

      if (error) throw error;

      await fetchProjects();
    } catch (error: any) {
      toast({
        title: "Error saving analysis",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Add chat message
  const addChatMessage = async (projectId: string, role: 'user' | 'assistant', content: string, metadata?: any) => {
    try {
      const { error } = await supabase
        .from('chat_messages')
        .insert({
          project_id: projectId,
          role,
          content,
          metadata
        });

      if (error) throw error;

      await fetchProjects();
    } catch (error: any) {
      toast({
        title: "Error saving message",
        description: error.message,
        variant: "destructive",
      });
    }
  };

  // Upload file to storage
  const uploadFile = async (projectId: string, file: File) => {
    try {
      // Validate file
      const fileValidation = validateInput.file(file);
      if (!fileValidation.isValid) {
        throw new Error(fileValidation.error);
      }

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) throw new Error('User not authenticated');

      const fileExt = file.name.split('.').pop();
      const sanitizedFileName = sanitizeInput.filename(file.name.replace(/\.[^/.]+$/, ""));
      const fileName = `${user.id}/${projectId}/${Date.now()}-${sanitizedFileName}.${fileExt}`;

      const { error: uploadError } = await supabase.storage
        .from('project-files')
        .upload(fileName, file);

      if (uploadError) throw uploadError;

      // Update project with file info
      await updateProject(projectId, {
        content: sanitizedFileName,
        file_name: file.name,
        file_size: file.size
      });

      return fileName;
    } catch (error: any) {
      toast({
        title: "Error uploading file",
        description: error.message,
        variant: "destructive",
      });
      throw error;
    }
  };

  // Get file URL
  const getFileUrl = (fileName: string) => {
    const { data } = supabase.storage
      .from('project-files')
      .getPublicUrl(fileName);
    return data.publicUrl;
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  return {
    projects,
    currentProject,
    loading,
    createProject,
    deleteProject,
    selectProject,
    updateProject,
    addAnalysisResult,
    addChatMessage,
    uploadFile,
    getFileUrl,
    refetch: fetchProjects
  };
};