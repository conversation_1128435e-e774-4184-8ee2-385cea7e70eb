import { useState } from "react";
import { Upload, FileText, Mic, Play, Pause, RotateCcw, X, FileIcon } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { Progress } from "@/components/ui/progress";
import { useProjectState } from "@/hooks/useProjectState";

interface AnalysisPanelProps {
  // No props needed - this is a static dashboard
}

// Complete story analysis data for all sections
const completeAnalysisData = {
  overall: {
    completion: 25,
    sectionsStarted: 4,
    totalSections: 9,
    lastUpdated: "2 hours ago"
  },
  sections: [
    {
      id: "path",
      title: "🛤️ PATH - Character Trajectory Foundation", 
      completion: 75,
      status: "Good Progress",
      summary: "Character arc well-established with clear transformation moments.",
      gaps: ["Missing specific obstacles in middle act", "Character motivation needs clarification"]
    },
    {
      id: "promise", 
      title: "🟡 PROMISE - What You're Selling Audiences",
      completion: 60,
      status: "Partial",
      summary: "Genre promises clearly established as psychological thriller.",
      gaps: ["Emotional promise unclear", "Resolution scope not defined"]
    },
    {
      id: "players",
      title: "🚗 PLAYERS - Action Initiators", 
      completion: 40,
      status: "Needs Work",
      summary: "Protagonist well-developed, supporting characters need depth.",
      gaps: ["Supporting characters lack depth", "Character relationships undefined"]
    },
    {
      id: "predicament",
      title: "🔴 PREDICAMENT - Why Nobody Can Walk Away",
      completion: 20, 
      status: "Started",
      summary: "Basic conflict established but dependencies unclear.",
      gaps: ["Character traps undefined", "Systemic pressure missing"]
    },
    {
      id: "people",
      title: "🚌 PEOPLE - Support System & World Pressure",
      completion: 10,
      status: "Minimal", 
      summary: "Environmental pressure systems not yet defined.",
      gaps: ["Support characters missing", "World pressure undefined"]
    },
    {
      id: "pull",
      title: "💫 PULL - Scene-Level Character Triangulation",
      completion: 0,
      status: "Not Started",
      summary: "Scene dynamics and character triangulation not addressed.",
      gaps: ["All micro-steps need attention"]
    },
    {
      id: "perspectives", 
      title: "🔄 PERSPECTIVES - Thematic Unity",
      completion: 0,
      status: "Not Started",
      summary: "Multiple storyline perspectives not yet explored.",
      gaps: ["Central themes undefined", "Perspective integration missing"]
    },
    {
      id: "plot",
      title: "📺 PLOT - 8-Episode Story Engine", 
      completion: 0,
      status: "Not Started",
      summary: "Episodic structure not yet developed.",
      gaps: ["Episode breakdown needed", "Story engine mechanics missing"]
    },
    {
      id: "pivot",
      title: "🔄 PIVOT - Strategic Information Management",
      completion: 0,
      status: "Not Started", 
      summary: "Information timing and revelation strategy not planned.",
      gaps: ["Revelation timing undefined", "Binge-worthy elements missing"]
    }
  ]
};

export const AnalysisPanel = ({}: AnalysisPanelProps) => {
  const [isRecording, setIsRecording] = useState(false);
  const [transcriptionText, setTranscriptionText] = useState("");
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  
  const { getCurrentProject, updateProjectFiles } = useProjectState();

  const { overall, sections } = completeAnalysisData;
  const currentProject = getCurrentProject();
  const projectFiles = currentProject?.analysisData.uploadedFiles || [];

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files && files.length > 0) {
      const fileNames = Array.from(files).map(file => file.name);
      setUploadedFiles(prev => [...prev, ...fileNames]);
      
      // Simulate upload progress
      setUploadProgress(0);
      const interval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 100) {
            clearInterval(interval);
            // Add files to project state when upload completes
            if (currentProject) {
              updateProjectFiles([...projectFiles, ...fileNames]);
            }
            return 100;
          }
          return prev + 10;
        });
      }, 200);
    }
  };

  const removeFile = (fileName: string) => {
    const updatedFiles = projectFiles.filter(file => file !== fileName);
    if (currentProject) {
      updateProjectFiles(updatedFiles);
    }
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
  };

  return (
    <div className="h-full flex flex-col p-6 overflow-y-auto">
      {/* Dashboard Header */}
      <div className="mb-6">
        <h2 className="text-3xl font-mono font-bold mb-2">
          <span className="neon-text">COMPLETE STORY ANALYSIS</span>
        </h2>
        <p className="text-muted-foreground">Comprehensive overview of all 9 P's framework sections</p>
        
        {/* Overall Stats */}
        <div className="flex gap-4 mt-4">
          <div className="cyber-panel p-4 flex-1">
            <div className="text-2xl font-mono font-bold neon-text">{overall.completion}%</div>
            <div className="text-sm text-muted-foreground">Overall Progress</div>
          </div>
          <div className="cyber-panel p-4 flex-1">
            <div className="text-2xl font-mono font-bold text-accent">{overall.sectionsStarted}/{overall.totalSections}</div>
            <div className="text-sm text-muted-foreground">Sections Started</div>
          </div>
          <div className="cyber-panel p-4 flex-1">
            <div className="text-sm font-mono text-muted-foreground">Last Updated</div>
            <div className="text-sm font-bold">{overall.lastUpdated}</div>
          </div>
        </div>
      </div>

      {/* All Sections Overview */}
      <Card className="mb-6 cyber-panel">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-mono">
            <span className="neon-text">9 P'S ANALYSIS OVERVIEW</span>
            <Badge variant="secondary" className="bg-primary/20 text-primary">Live</Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {sections.map((section) => (
              <div key={section.id} className="cyber-panel p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-mono font-semibold text-sm">{section.title}</h4>
                  <div className="flex items-center gap-2">
                    <Progress value={section.completion} className="w-20 h-2" />
                    <span className="text-xs font-mono">{section.completion}%</span>
                    <Badge 
                      variant={section.completion > 50 ? "default" : section.completion > 0 ? "secondary" : "outline"}
                      className="text-xs"
                    >
                      {section.status}
                    </Badge>
                  </div>
                </div>
                
                <p className="text-sm text-muted-foreground mb-2">{section.summary}</p>
                
                {section.gaps.length > 0 && (
                  <div className="text-xs">
                    <span className="text-destructive font-mono">Gaps: </span>
                    <span className="text-muted-foreground">{section.gaps.join(", ")}</span>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Compact Upload Section */}
      <Card className="mb-4 cyber-panel">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 font-mono text-sm">
            <Upload className="h-4 w-4 text-primary" />
            MATERIAL UPLOAD
            {projectFiles.length > 0 && (
              <Badge variant="secondary" className="bg-primary/20 text-primary text-xs">
                {projectFiles.length} files
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="border-2 border-dashed border-border rounded-lg p-4 text-center hover:border-primary transition-cyber cyber-button">
            <input
              type="file"
              multiple
              accept=".pdf,.docx,.txt,.md"
              onChange={handleFileUpload}
              className="hidden"
              id="file-upload"
            />
            <label htmlFor="file-upload" className="cursor-pointer">
              <FileText className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
              <p className="text-sm font-mono mb-1">
                {projectFiles.length > 0 ? 'Add more files' : 'Drop files or click to upload'}
              </p>
              <p className="text-xs text-muted-foreground">PDF, DOCX, TXT, MD files</p>
            </label>
          </div>
          
          {uploadProgress > 0 && (
            <div className="mt-3">
              <Progress value={uploadProgress} className="mb-1" />
              <p className="text-xs font-mono text-center">Processing: {uploadProgress}%</p>
            </div>
          )}

          {/* Uploaded Files List */}
          {projectFiles.length > 0 && (
            <div className="mt-4 space-y-2">
              <p className="text-xs font-mono text-muted-foreground">Uploaded Files:</p>
              {projectFiles.map((fileName, index) => (
                <div key={index} className="flex items-center justify-between bg-secondary/50 rounded p-2">
                  <div className="flex items-center gap-2">
                    <FileIcon className="h-3 w-3 text-primary" />
                    <span className="text-xs font-mono">{fileName}</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-6 w-6 p-0 text-destructive hover:text-destructive"
                    onClick={() => removeFile(fileName)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Compact Voice Transcription */}
      <Card className="mb-6 cyber-panel">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 font-mono text-sm">
            <Mic className="h-4 w-4 text-accent" />
            VOICE TRANSCRIPTION
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-3 mb-3">
            <Button
              onClick={toggleRecording}
              className={`cyber-button ${isRecording ? 'glow-pink' : ''}`}
              variant={isRecording ? "default" : "outline"}
              size="sm"
            >
              {isRecording ? <Pause className="h-3 w-3 mr-1" /> : <Play className="h-3 w-3 mr-1" />}
              {isRecording ? 'Stop' : 'Record'}
            </Button>
            
            <Button variant="ghost" size="sm" className="cyber-button">
              <RotateCcw className="h-3 w-3" />
            </Button>
            
            {isRecording && (
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-destructive rounded-full animate-pulse" />
                <span className="text-xs font-mono terminal-cursor">Recording</span>
              </div>
            )}
          </div>
          
          <Textarea
            placeholder="Transcribed text will appear here, or type directly..."
            value={transcriptionText}
            onChange={(e) => setTranscriptionText(e.target.value)}
            className="min-h-24 font-mono cyber-panel text-sm"
          />
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="flex gap-4">
        <Button className="flex-1 cyber-button gradient-cyber">
          Process Materials
        </Button>
        <Button variant="outline" className="flex-1 cyber-button">
          Clear Section
        </Button>
      </div>
    </div>
  );
};