import { Client } from '@notionhq/client';
import readline from 'readline';

// Configuration
const NOTION_API_KEY = 'ntn_B19147150581bivbyNsLkpLGiEGtKtyU6D0DyN06qO0dCC';
const NOTION_DATABASE_ID = '23f072203bb280b5a9adda599b9635e1';
const OPENROUTER_API_KEY = '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';

// Initialize Notion client
const notion = new Client({
  auth: NOTION_API_KEY,
});

// Function to query Notion database
async function queryNotionDatabase(searchTerm = '') {
  try {
    console.log(`🔍 Searching Notion database for: "${searchTerm}"`);

    // First, let's get the database schema to see available properties
    if (!searchTerm) {
      const dbInfo = await notion.databases.retrieve({ database_id: NOTION_DATABASE_ID });
      console.log('📋 Available properties:', Object.keys(dbInfo.properties));
    }

    const response = await notion.databases.query({
      database_id: NOTION_DATABASE_ID,
      filter: searchTerm ? {
        or: [
          {
            property: 'Title',
            title: {
              contains: searchTerm,
            },
          },
          {
            property: 'Content',
            rich_text: {
              contains: searchTerm,
            },
          },
        ],
      } : undefined,
      sorts: [
        {
          property: 'Title',
          direction: 'ascending',
        },
      ],
    });

    const results = response.results.map((page) => {
      // Get all properties dynamically
      const properties = {};
      Object.keys(page.properties).forEach(key => {
        const prop = page.properties[key];
        if (prop.type === 'title') {
          properties.title = prop.title?.[0]?.plain_text || 'Untitled';
        } else if (prop.type === 'multi_select') {
          properties.tags = prop.multi_select?.map(tag => tag.name) || [];
        } else if (prop.type === 'rich_text') {
          properties[key] = prop.rich_text?.[0]?.plain_text || '';
        }
      });

      return {
        id: page.id,
        title: properties.title || page.properties.Title?.title?.[0]?.plain_text || 'Untitled',
        content: page.properties.Content?.rich_text?.[0]?.plain_text || '',
        step: page.properties.Step?.rich_text?.[0]?.plain_text || '',
        source: page.properties.Source?.rich_text?.[0]?.plain_text || '',
        tags: properties.tags || [],
        properties,
        url: page.url,
      };
    });

    console.log(`📚 Found ${results.length} items in your story brain:`);
    results.forEach((item, index) => {
      console.log(`  ${index + 1}. ${item.title} [${item.tags.join(', ')}]`);
    });

    return results;
  } catch (error) {
    console.error('❌ Error querying Notion:', error.message);
    return [];
  }
}

// Function to call OpenRouter API
async function callOpenRouter(messages) {
  try {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': 'http://localhost:3000',
        'X-Title': 'Notion Story Brain Chat',
      },
      body: JSON.stringify({
        model: 'anthropic/claude-3.5-sonnet',
        messages,
        temperature: 0.7,
        max_tokens: 1000,
      }),
    });

    if (!response.ok) {
      throw new Error(`OpenRouter API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0].message.content;
  } catch (error) {
    console.error('❌ Error calling OpenRouter:', error.message);
    return 'Sorry, I had trouble connecting to the AI service.';
  }
}

// Main chat function
async function startChat() {
  console.log('🧠 Welcome to your Notion Story Brain Chat!');
  console.log('💡 I can help you explore your story elements and provide insights.');
  console.log('📝 Type "search [term]" to search your Notion database');
  console.log('🚪 Type "exit" to quit\n');

  // Get initial database overview
  const allItems = await queryNotionDatabase();
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const chatHistory = [
    {
      role: 'system',
      content: `You are a story analysis assistant with access to the user's story brain database. 

Current story elements in their database:
${allItems.map(item => `- ${item.title} (Tags: ${item.tags.join(', ')})`).join('\n')}

Help them explore their story elements, make connections, and provide creative insights. When they search for specific terms, I'll provide you with the relevant results from their Notion database.`
    }
  ];

  const askQuestion = () => {
    rl.question('\n💬 You: ', async (input) => {
      if (input.toLowerCase() === 'exit') {
        console.log('👋 Goodbye! Happy writing!');
        rl.close();
        return;
      }

      // Handle search commands
      if (input.toLowerCase().startsWith('search ')) {
        const searchTerm = input.slice(7).trim();
        const searchResults = await queryNotionDatabase(searchTerm);
        
        if (searchResults.length > 0) {
          console.log('\n🤖 AI: Here are the relevant items from your story brain:');
          searchResults.forEach((item, index) => {
            console.log(`   ${index + 1}. ${item.title} [${item.tags.join(', ')}]`);
          });
          console.log('\nWhat would you like to explore about these elements?');
        } else {
          console.log('\n🤖 AI: No items found matching that search term. Try a different keyword or ask me something else!');
        }
        
        askQuestion();
        return;
      }

      // Regular chat
      chatHistory.push({ role: 'user', content: input });
      
      console.log('\n🤖 AI: Thinking...');
      const aiResponse = await callOpenRouter(chatHistory);
      
      console.log(`\n🤖 AI: ${aiResponse}`);
      chatHistory.push({ role: 'assistant', content: aiResponse });
      
      askQuestion();
    });
  };

  askQuestion();
}

// Start the chat
startChat().catch(console.error);
