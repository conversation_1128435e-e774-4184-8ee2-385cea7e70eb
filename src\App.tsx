import { useState } from 'react';

const ninePsList = [
  'Path', 'Promise', 'Players', 'Plot', 'Pacing', 
  'Peril', 'Passion', 'Payoff', 'Purpose'
];

function App() {
  const [currentSection, setCurrentSection] = useState('Path');

  const handleSectionClick = (section: string) => {
    console.log('Clicked section:', section);
    setCurrentSection(section);
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Matrix background effect */}
      <div className="fixed inset-0 matrix-bg opacity-5 pointer-events-none" />
      
      {/* Header */}
      <header className="relative z-10 p-4 border-b border-border cyber-panel">
        <h1 className="text-2xl font-mono font-bold">
          <span className="glitch-text neon-text" data-text="CYBERPUNK">
            CYBERPUNK
          </span>
          <span className="text-accent ml-2">STORY ANALYZER</span>
        </h1>
      </header>
      
      {/* Three Column Layout */}
      <div className="relative z-10 grid grid-cols-3 h-[calc(100vh-4rem)]">
        {/* Left Column - Navigation */}
        <div className="p-4 border-r border-border cyber-panel">
          <h2 className="font-semibold mb-4 text-primary">The 9 P's</h2>
          <ul className="space-y-2">
            {ninePsList.map((item) => (
              <li key={item}>
                <button
                  onClick={() => handleSectionClick(item)}
                  className={`w-full text-left p-3 rounded transition-cyber cyber-button ${
                    currentSection === item 
                      ? 'bg-primary/20 text-primary border-primary glow-blue' 
                      : 'text-muted-foreground hover:text-foreground hover:border-primary/50'
                  }`}
                >
                  {item}
                </button>
              </li>
            ))}
          </ul>
        </div>
        
        {/* Middle Column - Analysis */}
        <div className="p-4 border-r border-border cyber-panel">
          <h2 className="font-semibold mb-4 text-primary">Analysis - {currentSection}</h2>
          <textarea 
            className="w-full h-32 p-3 bg-input border border-border rounded text-foreground placeholder:text-muted-foreground focus:border-primary focus:glow-blue outline-none transition-cyber"
            placeholder={`Enter your ${currentSection} analysis here...`}
          />
          <div className="mt-4">
            <h3 className="font-medium mb-2 text-accent">Analysis Results:</h3>
            <div className="p-4 bg-card border border-border rounded cyber-panel">
              <p className="text-card-foreground">Analysis for {currentSection} will appear here</p>
            </div>
          </div>
        </div>
        
        {/* Right Column - Chat */}
        <div className="p-4 cyber-panel">
          <h2 className="font-semibold mb-4 text-primary">AI Chat</h2>
          <div className="h-64 bg-card border border-border rounded p-4 mb-4 overflow-y-auto cyber-panel">
            <p className="text-muted-foreground">Chat messages will appear here...</p>
          </div>
          <div className="flex gap-2">
            <input 
              type="text" 
              className="flex-1 p-3 bg-input border border-border rounded text-foreground placeholder:text-muted-foreground focus:border-primary focus:glow-blue outline-none transition-cyber"
              placeholder="Ask about your story..."
            />
            <button className="px-4 py-2 bg-primary text-primary-foreground rounded cyber-button hover:glow-blue font-medium transition-cyber">
              Send
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
