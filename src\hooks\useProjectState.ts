import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface Project {
  id: string;
  name: string;
  description?: string;
  createdDate: string;
  lastModified: string;
  analysisData: {
    sections: Record<string, Record<string, string>>; // sectionId -> stepId -> content
    chatHistory: any[];
    uploadedFiles: string[];
  };
}

interface ProjectState {
  projects: Record<string, Project>;
  currentProjectId: string | null;
  
  // Project Management
  createProject: (name: string, description?: string) => string;
  deleteProject: (projectId: string) => void;
  setCurrentProject: (projectId: string) => void;
  getCurrentProject: () => Project | null;
  
  // Analysis Data Management
  updateMicroStepContent: (sectionId: string, stepId: string, content: string) => void;
  getMicroStepContent: (sectionId: string, stepId: string) => string;
  addChatMessage: (message: any) => void;
  getChatHistory: () => any[];
  updateProjectFiles: (files: string[]) => void;
  
  // Suggestions
  getSuggestion: (sectionId: string, stepId: string) => string;
}

const generateProjectId = () => `project-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

const getSuggestion = (sectionId: string, stepId: string, content: string): string => {
  const stepTitle = getStepTitle(sectionId, stepId);
  
  if (!content || content.length < 50) {
    return `This section is empty. Let's start with some foundational questions about ${stepTitle}.`;
  } else if (content.length < 200) {
    return `You have some content here. Let's expand and strengthen what you've written for ${stepTitle}.`;
  } else {
    return `Good coverage in this section. Want to refine it or review for gaps in ${stepTitle}?`;
  }
};

const getStepTitle = (sectionId: string, stepId: string): string => {
  const stepTitles: Record<string, Record<string, string>> = {
    'path': {
      '1.1': 'Choose Your Truth Pattern & Trajectory',
      '1.2': 'Define Dual Transformation Arc with Goal Generation',
      '1.3': 'Character Psychology Deep Dive'
    },
    'promise': {
      '2.1': 'Identify Genre + Trajectory Integration',
      '2.2': 'Genre Promise + Vicarious Adventure',
      '2.3': 'Establish Central POLARITY'
    },
    'players': {
      '3.1': 'Cast Your 4 Driver Characters',
      '3.2': 'Character Goal Relationships',
      '3.3': 'Storyline Distribution & Mini-Arcs'
    }
    // Add more as needed
  };
  
  return stepTitles[sectionId]?.[stepId] || `Step ${stepId}`;
};

const DEFAULT_TEST_PROJECT: Project = {
  id: 'default-test-project',
  name: 'Sample Story Project',
  description: 'Test project for the Terrible Trajectory framework',
  createdDate: new Date().toISOString(),
  lastModified: new Date().toISOString(),
  analysisData: {
    sections: {
      'path': {
        '1.1': 'Our protagonist starts as a lonely office worker who believes success means climbing the corporate ladder at any cost.',
        '1.2': 'Character arc: From ruthless ambition → discovering genuine human connection and purpose.'
      }
    },
    chatHistory: [],
    uploadedFiles: []
  }
};

export const useProjectState = create<ProjectState>()(
  persist(
    (set, get) => ({
      projects: {
        'default-test-project': DEFAULT_TEST_PROJECT // Include default project
      },
      currentProjectId: 'default-test-project', // Set default project as current
      
      createProject: (name: string, description?: string) => {
        const projectId = generateProjectId();
        const project: Project = {
          id: projectId,
          name,
          description,
          createdDate: new Date().toISOString(),
          lastModified: new Date().toISOString(),
          analysisData: {
            sections: {},
            chatHistory: [],
            uploadedFiles: []
          }
        };
        
        set(state => ({
          projects: { ...state.projects, [projectId]: project },
          currentProjectId: projectId
        }));
        
        return projectId;
      },
      
      deleteProject: (projectId: string) => {
        set(state => {
          const newProjects = { ...state.projects };
          delete newProjects[projectId];
          
          return {
            projects: newProjects,
            currentProjectId: state.currentProjectId === projectId ? null : state.currentProjectId
          };
        });
      },
      
      setCurrentProject: (projectId: string) => {
        set({ currentProjectId: projectId });
      },
      
      getCurrentProject: () => {
        const { projects, currentProjectId } = get();
        return currentProjectId ? projects[currentProjectId] || null : null;
      },
      
      updateMicroStepContent: (sectionId: string, stepId: string, content: string) => {
        const { currentProjectId } = get();
        if (!currentProjectId) return;
        
        set(state => {
          const project = state.projects[currentProjectId];
          if (!project) return state;
          
          return {
            projects: {
              ...state.projects,
              [currentProjectId]: {
                ...project,
                lastModified: new Date().toISOString(),
                analysisData: {
                  ...project.analysisData,
                  sections: {
                    ...project.analysisData.sections,
                    [sectionId]: {
                      ...project.analysisData.sections[sectionId],
                      [stepId]: content
                    }
                  }
                }
              }
            }
          };
        });
      },
      
      getMicroStepContent: (sectionId: string, stepId: string) => {
        const { projects, currentProjectId } = get();
        if (!currentProjectId) return '';
        
        const project = projects[currentProjectId];
        return project?.analysisData.sections[sectionId]?.[stepId] || '';
      },
      
      addChatMessage: (message: any) => {
        const { currentProjectId } = get();
        if (!currentProjectId) return;
        
        set(state => {
          const project = state.projects[currentProjectId];
          if (!project) return state;
          
          return {
            projects: {
              ...state.projects,
              [currentProjectId]: {
                ...project,
                lastModified: new Date().toISOString(),
                analysisData: {
                  ...project.analysisData,
                  chatHistory: [...project.analysisData.chatHistory, message]
                }
              }
            }
          };
        });
      },
      
      getChatHistory: () => {
        const { projects, currentProjectId } = get();
        if (!currentProjectId) return [];
        
        const project = projects[currentProjectId];
        return project?.analysisData.chatHistory || [];
      },
      
      
      updateProjectFiles: (files: string[]) => {
        const { currentProjectId } = get();
        if (!currentProjectId) return;
        
        set(state => {
          const project = state.projects[currentProjectId];
          if (!project) return state;
          
          return {
            projects: {
              ...state.projects,
              [currentProjectId]: {
                ...project,
                lastModified: new Date().toISOString(),
                analysisData: {
                  ...project.analysisData,
                  uploadedFiles: files
                }
              }
            }
          };
        });
      },
      
      getSuggestion: (sectionId: string, stepId: string) => {
        const content = get().getMicroStepContent(sectionId, stepId);
        return getSuggestion(sectionId, stepId, content);
      }
    }),
    {
      name: 'cyberpunk-projects',
      version: 1
    }
  )
);