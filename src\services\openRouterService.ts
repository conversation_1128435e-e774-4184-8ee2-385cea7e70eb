const OPENROUTER_API_KEY = import.meta.env.VITE_OPENROUTER_API_KEY;
const OPENROUTER_MODEL = import.meta.env.VITE_OPENROUTER_MODEL || 'anthropic/claude-3.5-sonnet';

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
}

export interface OpenRouterResponse {
  id: string;
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export const sendChatMessage = async (
  messages: ChatMessage[],
  model: string = OPENROUTER_MODEL
): Promise<string> => {
  try {
    if (!OPENROUTER_API_KEY) {
      throw new Error('OpenRouter API key not configured');
    }

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Cyber Scribe Logic - Story Analysis',
      },
      body: JSON.stringify({
        model,
        messages,
        temperature: 0.7,
        max_tokens: 1000,
        stream: false,
      }),
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(`OpenRouter API error: ${response.status} - ${errorData.error?.message || 'Unknown error'}`);
    }

    const data: OpenRouterResponse = await response.json();
    
    if (!data.choices || data.choices.length === 0) {
      throw new Error('No response from OpenRouter API');
    }

    return data.choices[0].message.content;
  } catch (error) {
    console.error('Error calling OpenRouter API:', error);
    throw error;
  }
};

export const generateStoryAnalysis = async (
  userMessage: string,
  currentSection: string,
  storyContext: string[] = [],
  chatHistory: ChatMessage[] = []
): Promise<string> => {
  const systemPrompt = `You are an expert story analyst and writing coach. You're helping a writer develop their story by analyzing the "${currentSection}" section.

${storyContext.length > 0 ? `
Relevant story elements from their brain:
${storyContext.map(context => `- ${context}`).join('\n')}
` : ''}

Provide insightful, specific, and actionable feedback. Focus on:
- Character development and motivation
- Plot structure and pacing
- Theme exploration
- Narrative techniques
- Areas for improvement

Keep your response conversational but professional, and always tie your advice back to the specific section they're working on.`;

  const messages: ChatMessage[] = [
    { role: 'system', content: systemPrompt },
    ...chatHistory.slice(-6), // Keep last 6 messages for context
    { role: 'user', content: userMessage }
  ];

  return await sendChatMessage(messages);
};
