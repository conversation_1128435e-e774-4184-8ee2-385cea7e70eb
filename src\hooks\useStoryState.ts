import { create } from 'zustand';
import { useProjectState } from './useProjectState';

interface MicroStep {
  id: string;
  title: string;
  completed: boolean;
  hasContent: boolean;
  content?: string;
}

interface Section {
  id: string;
  title: string;
  description: string;
  microSteps: MicroStep[];
  completion: number;
}

interface StoryState {
  currentSection: string;
  currentMicroStep: string | null;
  sections: Section[];
  setCurrentSection: (sectionId: string) => void;
  setCurrentMicroStep: (stepId: string | null) => void;
}

export const useStoryState = create<StoryState>((set, get) => ({
  currentSection: 'path',
  currentMicroStep: null,
  sections: [], // Will be initialized from existing data
  
  setCurrentSection: (sectionId: string) => 
    set({ currentSection: sectionId }),
    
  setCurrentMicroStep: (stepId: string | null) => 
    set({ currentMicroStep: stepId })
}));

// Hook to bridge story state with project state
export const useStoryWithProject = () => {
  const storyState = useStoryState();
  const projectState = useProjectState();
  
  return {
    ...storyState,
    getMicroStepContent: projectState.getMicroStepContent,
    updateMicroStepContent: projectState.updateMicroStepContent,
    getSuggestion: projectState.getSuggestion
  };
};