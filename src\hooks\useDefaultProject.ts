import { useState, useEffect } from 'react';

interface DefaultProject {
  id: string;
  name: string;
  content: string;
  created_at: string;
}

const DEFAULT_PROJECT: DefaultProject = {
  id: 'default-test-project',
  name: 'Sample Story Project',
  content: 'This is a test story for analyzing the 9 P\'s framework. Our protagonist starts their journey...',
  created_at: new Date().toISOString(),
};

export const useDefaultProject = () => {
  const [defaultProject, setDefaultProject] = useState<DefaultProject | null>(null);

  useEffect(() => {
    // Check if default project exists in localStorage
    const stored = localStorage.getItem('default-project');
    if (stored) {
      try {
        setDefaultProject(JSON.parse(stored));
      } catch (error) {
        console.error('Error parsing stored project:', error);
        // If corrupted, create new default
        createDefaultProject();
      }
    } else {
      // Create default project for testing
      createDefaultProject();
    }
  }, []);

  const createDefaultProject = () => {
    localStorage.setItem('default-project', JSON.stringify(DEFAULT_PROJECT));
    setDefaultProject(DEFAULT_PROJECT);
  };

  const clearDefaultProject = () => {
    localStorage.removeItem('default-project');
    setDefaultProject(null);
  };

  return {
    defaultProject,
    createDefaultProject,
    clearDefaultProject,
  };
};