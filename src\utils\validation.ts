export const validateInput = {
  // Project name validation
  projectName: (name: string): { isValid: boolean; error?: string } => {
    if (!name || typeof name !== 'string') {
      return { isValid: false, error: 'Project name is required' };
    }
    
    const trimmed = name.trim();
    if (trimmed.length < 1) {
      return { isValid: false, error: 'Project name cannot be empty' };
    }
    
    if (trimmed.length > 100) {
      return { isValid: false, error: 'Project name must be less than 100 characters' };
    }
    
    // Check for potentially dangerous characters
    if (/[<>\"'&]/.test(trimmed)) {
      return { isValid: false, error: 'Project name contains invalid characters' };
    }
    
    return { isValid: true };
  },

  // Description validation
  description: (desc: string): { isValid: boolean; error?: string } => {
    if (!desc) return { isValid: true }; // Optional field
    
    if (typeof desc !== 'string') {
      return { isValid: false, error: 'Description must be text' };
    }
    
    if (desc.length > 1000) {
      return { isValid: false, error: 'Description must be less than 1000 characters' };
    }
    
    return { isValid: true };
  },

  // File validation
  file: (file: File): { isValid: boolean; error?: string } => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    const allowedTypes = ['.txt', '.doc', '.docx', '.pdf', '.rtf'];
    
    if (file.size > maxSize) {
      return { isValid: false, error: 'File size must be less than 10MB' };
    }
    
    const extension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedTypes.includes(extension)) {
      return { isValid: false, error: `File type ${extension} is not allowed` };
    }
    
    // Check for potentially dangerous file names
    if (/[<>:"\\|?*]/.test(file.name)) {
      return { isValid: false, error: 'File name contains invalid characters' };
    }
    
    return { isValid: true };
  },

  // Content validation for analysis data
  analysisContent: (content: any): { isValid: boolean; error?: string } => {
    if (!content) return { isValid: true };
    
    // Ensure content is an object and not too large
    const jsonString = JSON.stringify(content);
    if (jsonString.length > 50000) { // 50KB limit
      return { isValid: false, error: 'Analysis content is too large' };
    }
    
    return { isValid: true };
  },

  // Chat message validation
  chatMessage: (message: string): { isValid: boolean; error?: string } => {
    if (!message || typeof message !== 'string') {
      return { isValid: false, error: 'Message is required' };
    }
    
    if (message.length > 5000) {
      return { isValid: false, error: 'Message is too long (max 5000 characters)' };
    }
    
    return { isValid: true };
  }
};

// Sanitize user input
export const sanitizeInput = {
  text: (input: string): string => {
    if (!input || typeof input !== 'string') return '';
    
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove angle brackets
      .substring(0, 1000); // Limit length
  },
  
  filename: (filename: string): string => {
    if (!filename || typeof filename !== 'string') return '';
    
    return filename
      .trim()
      .replace(/[<>:"\\|?*]/g, '_') // Replace dangerous chars with underscore
      .substring(0, 255); // Limit length
  }
};