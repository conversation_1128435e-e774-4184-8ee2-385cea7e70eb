import { Client } from '@notionhq/client';

const notion = new Client({
  auth: import.meta.env.VITE_NOTION_API_KEY,
});

const STORY_BRAIN_DATABASE_ID = import.meta.env.VITE_NOTION_DATABASE_ID;

export interface StoryElement {
  id: string;
  title: string;
  tags: string[];
  content: any;
  url: string;
  relevance?: 'high' | 'medium' | 'low';
}

export const queryStoryBrain = async (searchTerm?: string): Promise<StoryElement[]> => {
  try {
    if (!STORY_BRAIN_DATABASE_ID) {
      console.warn('Notion database ID not configured');
      return [];
    }

    const response = await notion.databases.query({
      database_id: STORY_BRAIN_DATABASE_ID,
      filter: searchTerm ? {
        or: [
          {
            property: 'Name',
            title: {
              contains: searchTerm,
            },
          },
          {
            property: 'Tags',
            multi_select: {
              contains: searchTerm,
            },
          },
        ],
      } : undefined,
      sorts: [
        {
          property: 'Name',
          direction: 'ascending',
        },
      ],
    });
    
    return response.results.map((page: any) => ({
      id: page.id,
      title: page.properties?.Name?.title?.[0]?.plain_text || 'Untitled',
      tags: page.properties?.Tags?.multi_select?.map((tag: any) => tag.name) || [],
      content: page.properties,
      url: page.url,
    }));
  } catch (error) {
    console.error('Error querying story brain:', error);
    return [];
  }
};

export const getStoryElement = async (elementId: string) => {
  try {
    const response = await notion.pages.retrieve({ page_id: elementId });
    return response;
  } catch (error) {
    console.error('Error getting story element:', error);
    throw error;
  }
};

export const searchStoryBrainBySection = async (section: string): Promise<StoryElement[]> => {
  const results = await queryStoryBrain(section);
  
  // Add relevance scoring based on tags and title matching
  return results.map(item => ({
    ...item,
    relevance: item.tags.some(tag => 
      tag.toLowerCase().includes(section.toLowerCase())
    ) ? 'high' : 
    item.title.toLowerCase().includes(section.toLowerCase()) ? 'medium' : 'low'
  })).sort((a, b) => {
    const relevanceOrder = { high: 3, medium: 2, low: 1 };
    return relevanceOrder[b.relevance!] - relevanceOrder[a.relevance!];
  });
};
