import { useState } from "react";
import { ChevronDown, ChevronRight, CheckCircle2, Circle, AlertCircle } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { useStoryWithProject } from "@/hooks/useStoryState";

interface NinePsSection {
  id: string;
  title: string;
  description: string;
  microSteps: {
    id: string;
    title: string;
    completed: boolean;
    hasContent: boolean;
  }[];
  completion: number;
}

const ninePsSections: NinePsSection[] = [
  {
    id: "path",
    title: "🛤️ 1. PATH - Character Trajectory Foundation",
    description: "Character's foundational journey and transformation patterns",
    completion: 75,
    microSteps: [
      { id: "1.1", title: "Choose Your Truth Pattern & Trajectory", completed: true, hasContent: true },
      { id: "1.2", title: "Define Dual Transformation Arc with Goal Generation", completed: true, hasContent: true },
      { id: "1.3", title: "Character Psychology Deep Dive", completed: false, hasContent: true }
    ]
  },
  {
    id: "promise",
    title: "🟡 2. PROMISE - What You're Selling Audiences",
    description: "The central commitment and contract with your audience",
    completion: 60,
    microSteps: [
      { id: "2.1", title: "Identify Genre + Trajectory Integration", completed: true, hasContent: true },
      { id: "2.2", title: "Genre Promise + Vicarious Adventure", completed: true, hasContent: false },
      { id: "2.3", title: "Establish Central POLARITY", completed: false, hasContent: false }
    ]
  },
  {
    id: "players",
    title: "🚗 3. PLAYERS - Action Initiators (4 Characters)",
    description: "The four driving characters who initiate action",
    completion: 40,
    microSteps: [
      { id: "3.1", title: "Cast Your 4 Driver Characters", completed: true, hasContent: true },
      { id: "3.2", title: "Character Goal Relationships", completed: false, hasContent: true },
      { id: "3.3", title: "Storyline Distribution & Mini-Arcs", completed: false, hasContent: false }
    ]
  },
  {
    id: "predicament",
    title: "🔴 4. PREDICAMENT - Why Nobody Can Walk Away",
    description: "The web of dependencies that trap all characters",
    completion: 20,
    microSteps: [
      { id: "4.1", title: "Individual Character Traps", completed: false, hasContent: true },
      { id: "4.2", title: "Ensemble Web of Dependencies", completed: false, hasContent: false },
      { id: "4.3", title: "Systemic Pressure Escalation", completed: false, hasContent: false }
    ]
  },
  {
    id: "people",
    title: "🚌 5. PEOPLE - Support System & World Pressure",
    description: "Support characters and environmental pressure systems",
    completion: 10,
    microSteps: [
      { id: "5.1", title: "Design 4 Support Character Functions", completed: false, hasContent: false },
      { id: "5.2", title: "Environmental Pressure Systems", completed: false, hasContent: false },
      { id: "5.3", title: "Powers That Be Integration", completed: false, hasContent: false }
    ]
  },
  {
    id: "pull",
    title: "💫 6. PULL - Scene-Level Character Triangulation",
    description: "Scene-by-scene character pressure and dynamics",
    completion: 0,
    microSteps: [
      { id: "6.1", title: "Master Character Triangulation Types", completed: false, hasContent: false },
      { id: "6.2", title: "Scene Pressure Generation", completed: false, hasContent: false },
      { id: "6.3", title: "Behavioral Signature Under Pressure", completed: false, hasContent: false }
    ]
  },
  {
    id: "perspectives",
    title: "🔄 7. PERSPECTIVES - Thematic Unity Through Multiple Angles",
    description: "Multiple storyline perspectives on central themes",
    completion: 0,
    microSteps: [
      { id: "7.1", title: "Central Impossible Question", completed: false, hasContent: false },
      { id: "7.2", title: "Multi-Storyline Theme Exploration", completed: false, hasContent: false },
      { id: "7.3", title: "Contradictory Perspective Integration", completed: false, hasContent: false }
    ]
  },
  {
    id: "plot",
    title: "📺 8. PLOT - 8-Episode Story Engine",
    description: "Episodic structure and story engine mechanics",
    completion: 0,
    microSteps: [
      { id: "8.1", title: "Episode-by-Episode Flaw Escalation", completed: false, hasContent: false },
      { id: "8.2", title: "Midpoint Fulcrum Crisis (Episode 4)", completed: false, hasContent: false },
      { id: "8.3", title: "Series Sustainability Engine", completed: false, hasContent: false }
    ]
  },
  {
    id: "pivot",
    title: "🔄 9. PIVOT - Strategic Information Management",
    description: "Information timing and binge-worthy addiction creation",
    completion: 0,
    microSteps: [
      { id: "9.1", title: "Strategic Revelation Timing", completed: false, hasContent: false },
      { id: "9.2", title: "Episode-Ending Pivot Types", completed: false, hasContent: false },
      { id: "9.3", title: "Binge-Worthy Addiction Creation", completed: false, hasContent: false }
    ]
  }
];

interface StoryNavigationProps {
  currentSection: string;
  onSectionChange: (section: string) => void;
  onMicroStepChange: (sectionId: string, stepId: string) => void;
}

export const StoryNavigation = ({ currentSection, onSectionChange, onMicroStepChange }: StoryNavigationProps) => {
  const { currentMicroStep, getMicroStepContent } = useStoryWithProject();
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set([currentSection])
  );

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const overallCompletion = Math.round(
    ninePsSections.reduce((sum, section) => sum + section.completion, 0) / ninePsSections.length
  );

  const getStepIcon = (sectionId: string, step: any) => {
    const content = getMicroStepContent(sectionId, step.id);
    if (step.completed) return <CheckCircle2 className="h-4 w-4 text-neon-green" />;
    if (content && content.length > 50) return <AlertCircle className="h-4 w-4 text-terminal-amber" />;
    return <Circle className="h-4 w-4 text-muted-foreground" />;
  };

  return (
    <div className="h-full flex flex-col p-4">
      {/* Overall Progress */}
      <div className="mb-6 p-4 cyber-panel">
        <div className="flex items-center justify-between mb-2">
          <h3 className="font-mono font-semibold neon-text">STORY ANALYSIS</h3>
          <Badge variant="secondary" className="bg-primary/20 text-primary">
            {overallCompletion}%
          </Badge>
        </div>
        <Progress 
          value={overallCompletion} 
          className="h-2 bg-secondary"
        />
        <p className="text-xs text-muted-foreground mt-2 font-mono">
          {ninePsSections.filter(s => s.completion > 0).length}/9 sections started
        </p>
      </div>

      {/* 9 P's Navigation */}
      <div className="flex-1 overflow-y-auto space-y-2">
        {ninePsSections.map((section) => (
          <div
            key={section.id}
            className={`transition-brutal mb-2 ${
              currentSection === section.id ? 'bg-primary/10' : 'bg-card'
            }`}
          >
            <Button
              variant="ghost"
              className="w-full p-3 h-auto justify-between hover:bg-muted border-0"
              onClick={() => {
                onSectionChange(section.id);
                toggleSection(section.id);
              }}
            >
              <div className="text-left">
                <div className="font-space-grotesk font-black text-sm uppercase">{section.title}</div>
                <div className="text-xs text-muted-foreground font-mono">{section.description}</div>
              </div>
              
              <div className="flex items-center gap-2">
                <Progress 
                  value={section.completion} 
                  className="w-16 h-1"
                />
                <span className="text-xs font-mono">{section.completion}%</span>
              </div>
            </Button>

            {/* Micro-steps */}
            {expandedSections.has(section.id) && (
              <div className="px-3 pb-2 space-y-1 bg-muted/30">
                {section.microSteps.map((step) => (
                  <Button
                    key={step.id}
                    variant="ghost"
                    className={`w-full justify-start gap-2 p-2 h-auto hover:bg-background border-0 ${
                      currentMicroStep === step.id ? 'bg-primary/20' : ''
                    }`}
                    onClick={() => onMicroStepChange(section.id, step.id)}
                  >
                    {getStepIcon(section.id, step)}
                    <span className="text-sm font-mono">{step.id}</span>
                    <span className="text-sm text-muted-foreground text-left flex-1">{step.title}</span>
                  </Button>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="mt-4 p-4 cyber-panel">
        <Button className="w-full mb-2 brutal-button bg-primary text-primary-foreground font-space-grotesk font-black uppercase">
          Generate Report
        </Button>
        <Button variant="outline" className="w-full brutal-button bg-card border-4 border-border font-space-grotesk font-bold uppercase">
          Export Data
        </Button>
      </div>
    </div>
  );
};