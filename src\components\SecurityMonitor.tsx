import { useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export const SecurityMonitor = () => {
  const { toast } = useToast();

  useEffect(() => {
    // Monitor auth state changes for security events
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        switch (event) {
          case 'PASSWORD_RECOVERY':
            toast({
              title: "Password Recovery",
              description: "Password recovery email sent. Check your inbox.",
            });
            break;
          
          case 'USER_UPDATED':
            toast({
              title: "Account Updated",
              description: "Your account information has been updated.",
            });
            break;
          
          case 'TOKEN_REFRESHED':
            // Silent refresh - no notification needed
            console.log('Security: Token refreshed successfully');
            break;
          
          case 'SIGNED_OUT':
            toast({
              title: "Signed Out",
              description: "You have been signed out for security.",
            });
            break;

          default:
            break;
        }
      }
    );

    // Monitor for potential security issues
    const checkSessionValidity = setInterval(async () => {
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.warn('Security: Session validation error:', error.message);
        return;
      }
      
      if (session && session.expires_at) {
        const expiresAt = new Date(session.expires_at * 1000);
        const now = new Date();
        const timeUntilExpiry = expiresAt.getTime() - now.getTime();
        
        // Warn user 5 minutes before expiry
        if (timeUntilExpiry > 0 && timeUntilExpiry < 5 * 60 * 1000) {
          toast({
            title: "Session Expiring Soon",
            description: "Your session will expire in a few minutes. Save your work.",
            variant: "destructive",
          });
        }
      }
    }, 60000); // Check every minute

    return () => {
      subscription.unsubscribe();
      clearInterval(checkSessionValidity);
    };
  }, [toast]);

  return null; // This component doesn't render anything
};