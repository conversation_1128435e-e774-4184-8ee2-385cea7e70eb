import { useState, useRef, useEffect } from "react";
import { <PERSON>, Bo<PERSON>, User, Lightbulb, Zap, Brain, Database } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useStoryWithProject } from "@/hooks/useStoryState";
import { useAIChat, Message } from "@/hooks/useAIChat";
import { StoryElement } from "@/services/notionService";

interface ChatPanelProps {
  currentSection: string;
}

const contextualSuggestions: Record<string, string[]> = {
  path: [
    "What's my character's biggest fear?",
    "How does my protagonist change?",
    "What obstacles block the journey?",
    "When does transformation happen?"
  ],
  promise: [
    "What genre am I writing in?",
    "What emotional experience do I promise?",
    "How will the story resolve?",
    "What makes this story unique?"
  ],
  players: [
    "Who is my antagonist?",
    "What do supporting characters want?",
    "How do relationships change?",
    "Who has the most influence?"
  ]
};

const mockResponses: Record<string, string> = {
  "What's my character's biggest fear?": "Based on your uploaded materials, your protagonist seems to fear abandonment and losing control. This manifests in their reluctance to trust others and their tendency to micromanage situations. Consider exploring how this fear drives their initial decisions and how overcoming it becomes part of their growth arc.",
  "How does my protagonist change?": "Your character shows a clear arc from isolation to connection. They begin as someone who pushes others away and end as someone who embraces vulnerability. The transformation happens gradually through three key moments in your story that force them to confront their assumptions.",
  "What obstacles block the journey?": "I've identified several obstacles in your materials: external conflicts with the antagonist, internal struggles with self-doubt, and relationship conflicts with the mentor figure. The most significant obstacle appears to be the protagonist's inability to trust their own judgment.",
  "What genre am I writing in?": "Your story elements suggest a psychological thriller with literary fiction overtones. The focus on internal character development combined with suspenseful plot elements creates this hybrid approach. Consider leaning more into thriller conventions to meet reader expectations."
};

export const ChatPanel = ({ currentSection }: ChatPanelProps) => {
  const { currentMicroStep, getMicroStepContent, getSuggestion } = useStoryWithProject();
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'ai',
      content: `Welcome to the story analysis chat! I'm here to help you develop the **${currentSection.toUpperCase()}** section of your story. I've analyzed your uploaded materials and can provide targeted insights.`,
      timestamp: new Date(),
      suggestions: contextualSuggestions[currentSection] || []
    }
  ]);
  const [inputValue, setInputValue] = useState("");
  const [isTyping, setIsTyping] = useState(false);
  const scrollAreaRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Update context when section or micro-step changes
    const content = currentMicroStep ? getMicroStepContent(currentSection, currentMicroStep) : '';
    const suggestion = currentMicroStep ? getSuggestion(currentSection, currentMicroStep) : '';
    
    let contextContent = `Now working on: **${currentSection.toUpperCase()}**`;
    if (currentMicroStep) {
      contextContent += ` → **Step ${currentMicroStep}**`;
    }
    
    if (suggestion) {
      contextContent += `\n\n${suggestion}`;
    }
    
    if (content) {
      contextContent += `\n\nI can see you have: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`;
    }
    
    contextContent += '\n\nWhat would you like to explore about this section?';
    
    const contextMessage: Message = {
      id: Date.now().toString(),
      type: 'ai',
      content: contextContent,
      timestamp: new Date(),
      suggestions: contextualSuggestions[currentSection] || []
    };
    setMessages(prev => [...prev, contextMessage]);
  }, [currentSection, currentMicroStep, getMicroStepContent, getSuggestion]);

  useEffect(() => {
    // Auto-scroll to bottom
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTop = scrollAreaRef.current.scrollHeight;
    }
  }, [messages]);

  const handleSendMessage = async (content: string) => {
    if (!content.trim()) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue("");
    setIsTyping(true);

    try {
      // Get story context from Notion
      const storyContext = await searchStoryBrainBySection(currentSection);

      // Convert chat history for AI
      const chatHistory = messages.slice(-6).map(msg => ({
        role: msg.type === 'user' ? 'user' : 'assistant',
        content: msg.content
      }));

      // Generate AI response with context
      const contextStrings = storyContext.slice(0, 5).map(item =>
        `${item.title} (${item.tags.join(', ')})`
      );

      const aiContent = await generateStoryAnalysis(
        content,
        currentSection,
        contextStrings,
        chatHistory
      );

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: aiContent,
        timestamp: new Date(),
        suggestions: [
          ...contextualSuggestions[currentSection]?.filter(s => s !== content).slice(0, 2) || [],
          ...storyContext.slice(0, 2).map(item => `Tell me about ${item.title}`)
        ].slice(0, 3)
      };

      setMessages(prev => [...prev, aiResponse]);
    } catch (error) {
      console.error('AI response error:', error);

      // Fallback response
      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'ai',
        content: `I'm having trouble connecting to the AI service right now. However, I can see you're working on the ${currentSection} section. Could you tell me more about what specific aspect you'd like help with?`,
        timestamp: new Date(),
        suggestions: contextualSuggestions[currentSection]?.filter(s => s !== content).slice(0, 3) || []
      };

      setMessages(prev => [...prev, aiResponse]);
    } finally {
      setIsTyping(false);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    handleSendMessage(suggestion);
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-4 border-b border-border brutal-card bg-card">
        <div className="flex items-center gap-2 mb-2">
          <h3 className="font-space-grotesk font-black brutal-text">AI STORY ANALYST</h3>
          <Badge variant="secondary" className="bg-primary/20 text-primary font-space-grotesk font-bold">
            ACTIVE
          </Badge>
        </div>
        <p className="text-xs text-muted-foreground font-mono break-words">
          Context: {currentSection.toUpperCase()}{currentMicroStep ? ` → Step ${currentMicroStep}` : ""} analysis
        </p>
      </div>

      {/* Messages */}
      <ScrollArea className="flex-1 p-4" ref={scrollAreaRef}>
        <div className="space-y-4">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.type === 'ai' && (
                <div className="w-8 h-8 bg-muted flex items-center justify-center font-space-grotesk font-black text-xs">
                  AI
                </div>
              )}
              
              <div
                className={`max-w-[80%] ${
                  message.type === 'user'
                    ? 'bg-primary text-primary-foreground brutal-card'
                    : 'bg-muted/80 brutal-card'
                } rounded-none p-3`}
              >
                <p className="text-sm leading-relaxed break-words">{message.content}</p>
                <div className="text-xs opacity-70 mt-1 font-mono">
                  {message.timestamp.toLocaleTimeString()}
                </div>
                
                {/* AI Suggestions */}
                {message.type === 'ai' && message.suggestions && message.suggestions.length > 0 && (
                  <div className="mt-3 p-3 bg-background/50 space-y-2">
                    <div className="flex items-center gap-2 text-xs font-space-grotesk font-bold text-muted-foreground uppercase">
                      SUGGESTED QUESTIONS:
                    </div>
                    {message.suggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        className="w-full text-left justify-start bg-background hover:bg-card border-2 border-border text-xs h-auto py-2 px-3 whitespace-normal"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        <span className="break-words text-left leading-tight">{suggestion}</span>
                      </Button>
                    ))}
                  </div>
                )}
              </div>
              
              {message.type === 'user' && (
                <div className="w-8 h-8 bg-primary flex items-center justify-center font-space-grotesk font-black text-xs text-primary-foreground">
                  YOU
                </div>
              )}
            </div>
          ))}
          
          {/* Typing Indicator */}
          {isTyping && (
            <div className="flex gap-3">
              <div className="w-8 h-8 bg-muted flex items-center justify-center font-space-grotesk font-black text-xs">
                AI
              </div>
              <div className="bg-muted/80 brutal-card rounded-none p-3">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" />
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{animationDelay: '0.2s'}} />
                  <div className="w-2 h-2 bg-primary rounded-full animate-pulse" style={{animationDelay: '0.4s'}} />
                  <span className="ml-2 text-xs font-mono text-muted-foreground">Analyzing...</span>
                </div>
              </div>
            </div>
          )}
        </div>
      </ScrollArea>

      {/* Input */}
      <div className="p-4 border-t border-border">
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask about your story..."
            className="brutal-input font-mono"
            onKeyDown={(e) => {
              if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                handleSendMessage(inputValue);
              }
            }}
          />
          <Button
            onClick={() => handleSendMessage(inputValue)}
            disabled={!inputValue.trim() || isTyping}
            className="brutal-button bg-primary text-primary-foreground"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        
        {/* Quick Actions */}
        <div className="flex gap-2 mt-2">
          <Button variant="ghost" size="sm" className="brutal-button bg-card hover:bg-muted font-space-grotesk font-bold text-xs px-3 py-2">
            GENERATE IDEAS
          </Button>
          <Button variant="ghost" size="sm" className="brutal-button bg-card hover:bg-muted font-space-grotesk font-bold text-xs px-3 py-2">
            ANALYZE GAPS
          </Button>
        </div>
      </div>
    </div>
  );
};