import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { <PERSON><PERSON>, DialogContent, Di<PERSON>Header, <PERSON><PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { FileUpload } from "@/components/FileUpload";

interface CreateProjectModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onCreateProject: (name: string, description?: string, file?: File) => void;
}

export const CreateProjectModal = ({ open, onOpenChange, onCreateProject }: CreateProjectModalProps) => {
  const [projectName, setProjectName] = useState("");
  const [projectDescription, setProjectDescription] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isCreating, setIsCreating] = useState(false);

  const handleCreate = async () => {
    if (!projectName.trim()) return;

    setIsCreating(true);
    
    try {
      await onCreateProject(projectName.trim(), projectDescription.trim() || undefined, selectedFile || undefined);
      setProjectName("");
      setProjectDescription("");
      setSelectedFile(null);
      onOpenChange(false);
    } catch (error) {
      // Error handling is done in the parent component
    } finally {
      setIsCreating(false);
    }
  };

  const handleClose = () => {
    setProjectName("");
    setProjectDescription("");
    setSelectedFile(null);
    onOpenChange(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
      handleCreate();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="cyber-panel max-w-md">
        <DialogHeader>
          <DialogTitle className="font-mono neon-text">CREATE NEW PROJECT</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4" onKeyDown={handleKeyDown}>
          <div className="space-y-2">
            <Label htmlFor="project-name" className="font-mono text-sm">
              Project Name *
            </Label>
            <Input
              id="project-name"
              value={projectName}
              onChange={(e) => setProjectName(e.target.value)}
              placeholder="My Cyberpunk Thriller"
              className="cyber-panel font-mono"
              autoFocus
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="project-description" className="font-mono text-sm">
              Description (Optional)
            </Label>
            <Textarea
              id="project-description"
              value={projectDescription}
              onChange={(e) => setProjectDescription(e.target.value)}
              placeholder="A brief description of your story..."
              className="cyber-panel font-mono min-h-20"
              rows={3}
            />
          </div>
          
          <div className="space-y-2">
            <Label className="font-mono text-sm">
              Story File (Optional)
            </Label>
            <FileUpload
              onFileSelect={setSelectedFile}
              onFileRemove={() => setSelectedFile(null)}
              selectedFile={selectedFile}
            />
          </div>
          
          <div className="text-xs text-muted-foreground font-mono">
            💡 Tip: Use Ctrl+Enter to create quickly
          </div>
        </div>

        <DialogFooter className="gap-2">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="cyber-button"
            disabled={isCreating}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleCreate}
            disabled={!projectName.trim() || isCreating}
            className="cyber-button gradient-cyber"
          >
            {isCreating ? (
              <>
                <div className="w-4 h-4 border-2 border-transparent border-t-current border-r-current rounded-full animate-spin mr-2" />
                Creating...
              </>
            ) : (
              'Create Project'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};