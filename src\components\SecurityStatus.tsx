import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Shield, CheckCircle, AlertTriangle, Info } from 'lucide-react';
import { useSecurityCheck } from '@/hooks/useSecurityCheck';

export const SecurityStatus = () => {
  const [showDetails, setShowDetails] = useState(false);
  const { securityStatus, runSecurityChecks } = useSecurityCheck();

  const getOverallStatus = () => {
    const checks = Object.values(securityStatus);
    const passed = checks.filter(Boolean).length;
    const total = checks.length;
    
    if (passed === total) return 'secure';
    if (passed >= total * 0.8) return 'warning';
    return 'danger';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'secure':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'danger':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'secure':
        return <Badge className="bg-green-500/20 text-green-400 border-green-500/50">Secure</Badge>;
      case 'warning':
        return <Badge className="bg-yellow-500/20 text-yellow-400 border-yellow-500/50">Warning</Badge>;
      case 'danger':
        return <Badge className="bg-red-500/20 text-red-400 border-red-500/50">Risk</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const overallStatus = getOverallStatus();

  return (
    <Card className="cyber-panel">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Shield className="h-5 w-5 text-primary" />
            <CardTitle className="font-mono text-sm">Security Status</CardTitle>
          </div>
          <div className="flex items-center gap-2">
            {getStatusIcon(overallStatus)}
            {getStatusBadge(overallStatus)}
          </div>
        </div>
      </CardHeader>
      
      {showDetails && (
        <CardContent className="space-y-3">
          <div className="grid gap-2 text-xs font-mono">
            <div className="flex justify-between">
              <span>Authentication:</span>
              {securityStatus.isAuthenticated ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
            </div>
            
            <div className="flex justify-between">
              <span>Valid Session:</span>
              {securityStatus.hasValidSession ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
            </div>
            
            <div className="flex justify-between">
              <span>Database Access:</span>
              {securityStatus.canAccessOwnData ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
            </div>
            
            <div className="flex justify-between">
              <span>Data Isolation:</span>
              {securityStatus.cannotAccessOthersData ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
            </div>
            
            <div className="flex justify-between">
              <span>Storage Access:</span>
              {securityStatus.storageAccess ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertTriangle className="h-4 w-4 text-red-500" />
              )}
            </div>
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={runSecurityChecks}
            className="w-full cyber-button text-xs"
          >
            Recheck Security
          </Button>
        </CardContent>
      )}
      
      <div className="px-4 pb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setShowDetails(!showDetails)}
          className="w-full text-xs"
        >
          {showDetails ? 'Hide Details' : 'Show Details'}
        </Button>
      </div>
    </Card>
  );
};