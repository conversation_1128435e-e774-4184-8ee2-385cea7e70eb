import { useState } from 'react';
import { queryStoryBrain, searchStoryBrainBySection, StoryElement } from '@/services/notionService';
import { generateStoryAnalysis, ChatMessage } from '@/services/openRouterService';
import { useToast } from '@/hooks/use-toast';

export interface Message {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  storyContext?: StoryElement[];
}

export const useAIChat = () => {
  const [loading, setLoading] = useState(false);
  const [storyBrainLoading, setStoryBrainLoading] = useState(false);
  const { toast } = useToast();

  const searchStoryBrain = async (query?: string): Promise<StoryElement[]> => {
    setStoryBrainLoading(true);
    try {
      const results = await queryStoryBrain(query);
      return results;
    } catch (error: any) {
      toast({
        title: "Error accessing story brain",
        description: error.message,
        variant: "destructive",
      });
      return [];
    } finally {
      setStoryBrainLoading(false);
    }
  };

  const getStoryContext = async (section: string): Promise<StoryElement[]> => {
    try {
      const results = await searchStoryBrainBySection(section);
      return results.slice(0, 5); // Limit to top 5 most relevant
    } catch (error: any) {
      console.error('Error getting story context:', error);
      return [];
    }
  };

  const generateAIResponse = async (
    userMessage: string,
    currentSection: string,
    chatHistory: Message[] = []
  ): Promise<{ content: string; storyContext: StoryElement[] }> => {
    setLoading(true);
    try {
      // Get relevant story context from Notion
      const storyContext = await getStoryContext(currentSection);
      
      // Convert chat history to OpenRouter format
      const openRouterHistory: ChatMessage[] = chatHistory
        .slice(-10) // Keep last 10 messages
        .map(msg => ({
          role: msg.type === 'user' ? 'user' : 'assistant',
          content: msg.content
        }));

      // Generate AI response with story context
      const contextStrings = storyContext.map(item => 
        `${item.title} (${item.tags.join(', ')})`
      );

      const aiResponse = await generateStoryAnalysis(
        userMessage,
        currentSection,
        contextStrings,
        openRouterHistory
      );

      return {
        content: aiResponse,
        storyContext
      };
    } catch (error: any) {
      toast({
        title: "Error generating AI response",
        description: error.message,
        variant: "destructive",
      });
      
      // Fallback response
      return {
        content: `I'm having trouble connecting to the AI service right now. However, I can see you're working on the ${currentSection} section. Could you tell me more about what specific aspect you'd like help with?`,
        storyContext: []
      };
    } finally {
      setLoading(false);
    }
  };

  const getSuggestions = (currentSection: string, storyContext: StoryElement[] = []): string[] => {
    const baseSuggestions = {
      'path': [
        "What's the main character's motivation?",
        "How does the inciting incident change everything?",
        "What obstacles will challenge the protagonist?"
      ],
      'character': [
        "What drives this character's decisions?",
        "How do they change throughout the story?",
        "What's their biggest fear or weakness?"
      ],
      'world': [
        "How does the setting affect the story?",
        "What are the rules of this world?",
        "How does the environment create conflict?"
      ],
      'theme': [
        "What's the deeper meaning of this story?",
        "How do the themes emerge through action?",
        "What questions does your story explore?"
      ]
    };

    const contextSuggestions = storyContext.slice(0, 2).map(item => 
      `Tell me more about ${item.title}`
    );

    return [
      ...(baseSuggestions[currentSection] || []),
      ...contextSuggestions
    ].slice(0, 4);
  };

  return {
    searchStoryBrain,
    getStoryContext,
    generateAIResponse,
    getSuggestions,
    loading,
    storyBrainLoading,
  };
};
