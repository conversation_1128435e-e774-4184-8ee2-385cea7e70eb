import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Plus, FolderOpen, Calendar, Trash2, ExternalLink, LogOut, User } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Session, User as SupabaseUser } from "@supabase/supabase-js";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { CreateProjectModal } from "@/components/CreateProjectModal";
import { useSupabaseProjectState } from "@/hooks/useSupabaseProjectState";

export const ProjectSelect = () => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [user, setUser] = useState<SupabaseUser | null>(null);
  const { 
    projects, 
    currentProject, 
    loading,
    createProject,
    deleteProject,
    selectProject
  } = useSupabaseProjectState();
  
  // Projects are already an array from Supabase
  const projectsArray = projects;
  const navigate = useNavigate();
  const { toast } = useToast();

  // Get current user for display
  useEffect(() => {
    supabase.auth.getUser().then(({ data: { user } }) => {
      setUser(user);
    });
  }, []);

  // Show loading state while fetching projects
  if (loading) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="text-center">
          <div className="brutal-card p-8 bg-card">
            <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
            <p className="font-space-grotesk font-bold">Loading projects...</p>
          </div>
        </div>
      </div>
    );
  }

  const handleCreateProject = async (name: string, description?: string, file?: File) => {
    try {
      const projectId = await createProject(name, description);
      await selectProject(projectId);
      navigate('/analysis');
    } catch (error) {
      console.error('Error creating project:', error);
    }
  };

  const handleOpenProject = async (projectId: string) => {
    await selectProject(projectId);
    navigate('/analysis');
  };

  const handleDeleteProject = (projectId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    if (confirm('Are you sure you want to delete this project? This cannot be undone.')) {
      deleteProject(projectId);
    }
  };

  const handleSignOut = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      toast({
        title: "Signed out",
        description: "You've been successfully signed out.",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to sign out.",
        variant: "destructive",
      });
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Header */}
      <header className="relative z-10 p-6 border-b-4 border-border brutal-card bg-card">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-space-grotesk font-black brutal-heading mb-2">
              TERRIBLE TRAJECTORY
            </h1>
            <p className="text-lg font-space-grotesk font-bold text-primary mb-1">
              From Bad to Bingeable Story Development Framework
            </p>
            <p className="text-muted-foreground font-mono text-sm">
              AI-powered story analysis using the 9 P's framework
            </p>
          </div>
          
          {/* User Menu */}
          {user && (
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm font-mono">
                <User className="h-4 w-4" />
                <span className="text-muted-foreground">
                  {user.email}
                </span>
              </div>
              <Button
                onClick={handleSignOut}
                variant="outline"
                size="sm"
                className="brutal-button bg-card hover:bg-muted font-space-grotesk font-bold"
              >
                Sign Out
              </Button>
            </div>
          )}
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 p-6">
        <div className="max-w-4xl mx-auto">
          {/* Create New Project Section */}
          <div className="mb-8">
            <Button
              onClick={() => setShowCreateModal(true)}
              className="h-20 text-lg brutal-button bg-primary text-primary-foreground font-space-grotesk font-black uppercase w-full sm:w-auto"
              size="lg"
            >
              Create New Project
            </Button>
          </div>

          {/* Existing Projects */}
          {projectsArray.length > 0 && (
            <div>
              <h2 className="text-xl font-space-grotesk font-black brutal-text mb-4">
                Your Projects ({projectsArray.length})
              </h2>
              
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                {projectsArray.map((project) => (
                  <Card
                    key={project.id}
                    className="brutal-card hover:shadow-primary transition-brutal cursor-pointer group"
                    onClick={() => handleOpenProject(project.id)}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-start justify-between">
                        <CardTitle className="font-space-grotesk font-black text-lg group-hover:text-primary transition-brutal">
                          {project.title}
                        </CardTitle>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 opacity-0 group-hover:opacity-100 transition-brutal text-destructive hover:text-destructive brutal-button"
                          onClick={(e) => handleDeleteProject(project.id, e)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      {project.description && (
                        <p className="text-sm text-muted-foreground leading-relaxed">
                          {project.description}
                        </p>
                      )}
                    </CardHeader>
                    
                    <CardContent>
                      <div className="space-y-3">
                        <div className="flex items-center gap-2 text-xs text-muted-foreground font-mono">
                          <Calendar className="h-3 w-3" />
                          <span>Created {formatDate(project.created_at)}</span>
                        </div>
                        
                        <div className="flex items-center gap-2 text-xs text-muted-foreground font-mono">
                          <FolderOpen className="h-3 w-3" />
                          <span>Modified {formatDate(project.updated_at)}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <div className="flex gap-1">
                            <Badge variant="secondary" className="text-xs">
                              {project.analysis_results?.length || 0} Analysis
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {project.chat_messages?.length || 0} Messages
                            </Badge>
                            {project.file_name && (
                              <Badge variant="secondary" className="text-xs">
                                File
                              </Badge>
                            )}
                          </div>
                          
                          <ExternalLink className="h-4 w-4 text-primary opacity-0 group-hover:opacity-100 transition-brutal" />
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          )}

          {/* Empty State */}
          {projectsArray.length === 0 && (
            <div className="text-center py-12">
              <div className="brutal-card p-8 max-w-md mx-auto bg-card">
                <FolderOpen className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-lg font-space-grotesk font-black brutal-text mb-2">No Projects Yet</h3>
                <p className="text-muted-foreground text-sm mb-4">
                  Create your first project to start analyzing your story with the 9 P's framework.
                </p>
                <Button 
                  onClick={() => setShowCreateModal(true)}
                  className="brutal-button bg-primary text-primary-foreground font-space-grotesk font-bold"
                >
                  Get Started
                </Button>
              </div>
            </div>
          )}
        </div>
      </main>

      {/* Create Project Modal */}
      <CreateProjectModal
        open={showCreateModal}
        onOpenChange={setShowCreateModal}
        onCreateProject={handleCreateProject}
      />
    </div>
  );
};
