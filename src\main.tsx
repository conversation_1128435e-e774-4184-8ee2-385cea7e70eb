import { createRoot } from 'react-dom/client'
import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { ProjectSelect } from './pages/ProjectSelect'
import { Auth } from './pages/Auth'
import { Toaster } from '@/components/ui/toaster'
import { AuthGuard } from '@/components/AuthGuard'
import { SecurityMonitor } from '@/components/SecurityMonitor'
import Index from './pages/Index'
import './index.css'

createRoot(document.getElementById("root")!).render(
  <BrowserRouter>
    <SecurityMonitor />
    <Routes>
      <Route path="/auth" element={
        <AuthGuard requireAuth={false}>
          <Auth />
        </AuthGuard>
      } />
      <Route path="/" element={
        <AuthGuard requireAuth={true}>
          <ProjectSelect />
        </AuthGuard>
      } />
      <Route path="/analysis" element={
        <AuthGuard requireAuth={true}>
          <Index />
        </AuthGuard>
      } />
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
    <Toaster />
  </BrowserRouter>
);
