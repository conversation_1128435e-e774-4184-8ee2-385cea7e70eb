import { useEffect, useState, ReactNode } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { Session, User } from '@supabase/supabase-js';
import { useNavigate, useLocation } from 'react-router-dom';
import { useSecurityCheck } from '@/hooks/useSecurityCheck';

interface AuthGuardProps {
  children: ReactNode;
  requireAuth?: boolean;
}

export const AuthGuard = ({ children, requireAuth = true }: AuthGuardProps) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();
  const location = useLocation();
  const { securityStatus } = useSecurityCheck();

  useEffect(() => {
    // Set up auth state listener
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, session) => {
        setSession(session);
        setUser(session?.user ?? null);
        setLoading(false);

        // Security: Handle auth events
        if (event === 'SIGNED_OUT' || event === 'TOKEN_REFRESHED') {
          if (!session && requireAuth) {
            navigate('/auth');
          }
        }
        
        if (event === 'SIGNED_IN' && session) {
          // Redirect authenticated users away from auth page
          if (location.pathname === '/auth') {
            navigate('/');
          }
        }
      }
    );

    // Check for existing session
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
      setUser(session?.user ?? null);
      setLoading(false);

      // Route protection
      if (requireAuth && !session) {
        navigate('/auth');
      } else if (!requireAuth && session && location.pathname === '/auth') {
        navigate('/');
      }
    });

    return () => subscription.unsubscribe();
  }, [navigate, location.pathname, requireAuth]);

  // Show loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="cyber-panel p-8">
          <div className="w-8 h-8 border-2 border-transparent border-t-primary border-r-primary rounded-full animate-spin mx-auto mb-4" />
          <p className="font-mono text-center">Authenticating...</p>
        </div>
      </div>
    );
  }

  // Protect routes that require authentication
  if (requireAuth && !session?.user) {
    return null; // Will redirect to auth via useEffect
  }

  // Provide auth context to children
  return (
    <div data-auth-user={user?.id} data-auth-status={securityStatus.isAuthenticated}>
      {children}
    </div>
  );
};