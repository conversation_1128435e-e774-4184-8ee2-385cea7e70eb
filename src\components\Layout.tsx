import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Menu, X, Upload, MessageSquare, BarChart3, FolderOpen, ArrowLeft } from "lucide-react";
import { Button } from "@/components/ui/button";
import { StoryNavigation } from "./StoryNavigation";
import { AnalysisPanel } from "./AnalysisPanel";
import { ChatPanel } from "./ChatPanel";
import { useStoryWithProject } from "@/hooks/useStoryState";
import { useProjectState } from "@/hooks/useProjectState";

export const Layout = () => {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { currentSection, currentMicroStep, setCurrentSection, setCurrentMicroStep } = useStoryWithProject();
  const { getCurrentProject } = useProjectState();
  const navigate = useNavigate();
  
  const currentProject = getCurrentProject();
  
  // Note: Removed the useEffect redirect to prevent loops
  // Projects should be managed at the routing level instead

  return (
    <div className="min-h-screen bg-background text-foreground w-full overflow-hidden">
      {/* Header */}
      <header className="relative z-10 h-16 border-b-4 border-border brutal-card bg-card flex items-center justify-between px-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate('/')}
            className="brutal-button bg-muted hover:bg-secondary"
            title="Back to Projects"
          >
            <ArrowLeft className="h-5 w-5" />
          </Button>
          
          <Button
            variant="ghost"
            size="icon"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="brutal-button bg-muted hover:bg-secondary"
          >
            {sidebarOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
          
          <div>
            <h1 className="text-xl font-space-grotesk font-black brutal-heading">
              TERRIBLE TRAJECTORY
            </h1>
            {currentProject && (
              <div className="flex items-center gap-2 mt-1">
                <span className="text-sm font-mono text-muted-foreground">PROJECT:</span>
                <span className="text-sm font-space-grotesk font-bold text-foreground">{currentProject.name}</span>
              </div>
            )}
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="ghost" size="icon" className="brutal-button bg-muted hover:bg-secondary">
            <Upload className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="brutal-button bg-muted hover:bg-secondary">
            <BarChart3 className="h-5 w-5" />
          </Button>
          <Button variant="ghost" size="icon" className="brutal-button bg-muted hover:bg-secondary">
            <MessageSquare className="h-5 w-5" />
          </Button>
        </div>
      </header>

      <div className="flex h-[calc(100vh-4rem)] w-full">
        {/* Left Sidebar - 9 P's Navigation */}
        <aside 
          className={`${
            sidebarOpen ? 'w-80' : 'w-0'
          } transition-all duration-300 border-r-4 border-border bg-card overflow-hidden`}
        >
          <StoryNavigation 
            currentSection={currentSection}
            onSectionChange={setCurrentSection}
            onMicroStepChange={(sectionId, stepId) => {
              setCurrentSection(sectionId);
              setCurrentMicroStep(stepId);
            }}
          />
        </aside>

        {/* Center Panel - Complete Analysis Dashboard */}
        <main className="flex-1 bg-background m-2 ml-0 brutal-card">
          <AnalysisPanel />
        </main>

        {/* Right Panel - AI Chat */}
        <aside className="w-96 border-l-4 border-border bg-card">
          <ChatPanel currentSection={currentSection} />
        </aside>
      </div>
    </div>
  );
};