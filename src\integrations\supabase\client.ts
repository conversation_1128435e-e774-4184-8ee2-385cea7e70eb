// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://frwfefefaixjumjfxeoo.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZyd2ZlZmVmYWl4anVtamZ4ZW9vIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTM3MTAyMzIsImV4cCI6MjA2OTI4NjIzMn0.r2MpUceRPzMRdRMOZrIITnC3Zc4LXDCYLTiDqUenXV8";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});